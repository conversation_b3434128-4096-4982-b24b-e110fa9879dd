import { CardLMPContainer } from "@/shared/components/custom/card";
import { Input } from "@/shared/components/ui/input";
import { motion } from "framer-motion";
import { AlertCircle, Calculator, CheckCircle2, DollarSign, Percent } from "lucide-react";
import { useEffect, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { convertToNumber, formatCurrency } from "../../utils/currency-converter.util";
import { ProductFormData } from "../../validators/product/product-form.schema";
import { InputField } from "./input-field";

interface PricingCardProps {
	methods: UseFormReturn<ProductFormData>;
	isSubmitting: boolean;
	isInEditMode?: boolean;
	onSubmit: () => void;
}

export const PricingCard: React.FC<PricingCardProps> = ({ methods, isSubmitting, isInEditMode = false, onSubmit }): JSX.Element => {
	const { watch, setValue } = methods;
	const costPrice = watch("costPrice") || 0;
	const price = watch("price") || 0;
	const [marginPercentage, setMarginPercentage] = useState<number | undefined>(undefined);

	const suggestedMargins = [
		{ value: 20, label: "Básica" },
		{ value: 30, label: "Padrão" },
		{ value: 40, label: "Premium" },
	];

	const calculateProfit = (): { value: number; margin: number; roi: number } => {
		if (!costPrice || !price) return { value: 0, margin: 0, roi: 0 };

		const numCostPrice = Number(costPrice);
		const numPrice = Number(price);

		const profit = numPrice - numCostPrice;
		const margin = numPrice > 0 ? (profit / numPrice) * 100 : 0;
		const roi = numCostPrice > 0 ? (profit / numCostPrice) * 100 : 0;

		return {
			value: Number(profit.toFixed(2)),
			margin: Number(margin.toFixed(2)),
			roi: Number(roi.toFixed(2)),
		};
	};

	const calculatePriceFromMargin = (margin: number): number => {
		if (!costPrice || margin === undefined) return 0;
		if (margin === 100) return costPrice * 100;
		const price = costPrice / (1 - margin / 100);
		return Number(price.toFixed(2));
	};

	useEffect(() => {
		if (costPrice > 0 && price > 0) {
			const profit = price - costPrice;
			const newMargin = price > 0 ? (profit / price) * 100 : 0;
			setMarginPercentage(Number(newMargin.toFixed(2)));
		}
	}, [costPrice, price]);

	const handleCostPriceChange = (numericValue: number | undefined): void => {
		const validValue = numericValue && !isNaN(numericValue) ? numericValue : 0;
		setValue("costPrice", validValue, {
			shouldValidate: true,
			shouldDirty: true,
		});

		if (marginPercentage !== undefined && validValue > 0) {
			const calculatedPrice = calculatePriceFromMargin(marginPercentage);
			setValue("price", calculatedPrice, {
				shouldValidate: true,
				shouldDirty: true,
			});
		}
	};

	const handlePriceChange = (numericValue: number | undefined): void => {
		const validValue = numericValue && !isNaN(numericValue) ? numericValue : 0;
		setValue("price", validValue, {
			shouldValidate: true,
			shouldDirty: true,
		});

		if (costPrice > 0 && validValue > 0) {
			const profit = validValue - costPrice;
			const margin = (profit / validValue) * 100;
			setMarginPercentage(Number(margin.toFixed(2)));
		}
	};

	return (
		<CardLMPContainer icon={<DollarSign size={22} className="text-gray-500" />} title="Preços" description="Configure os preços do produto">
			<div className="space-y-6">
				<div className="relative group">
					<InputField
						label="Preço de Custo"
						name="costPrice"
						type="number"
						placeholder="Ex: 380,00"
						icon={<DollarSign size={16} />}
						methods={methods}
						onChange={e => {
							const value = e.target.value;
							if (!value || value.trim() === "") {
								handleCostPriceChange(0);
								return;
							}

							const numericValue = convertToNumber(value);
							if (numericValue >= 0) {
								handleCostPriceChange(numericValue);
							}
						}}
					/>
				</div>

				<div className="bg-gray-50/50 rounded-xl p-5 border border-gray-100 space-y-4">
					<div className="flex items-center gap-2">
						<div className="p-1.5 bg-mainColor/10 rounded-lg">
							<Percent size={18} className="text-mainColor" />
						</div>
						<label className="text-sm font-medium text-gray-700">Margem de Lucro</label>
					</div>

					<div className="flex gap-2 mt-4">
						{suggestedMargins.map(({ value, label }) => (
							<button
								key={value}
								type="button"
								onClick={() => {
									setMarginPercentage(value);
									if (costPrice) {
										const calculatedPrice = calculatePriceFromMargin(value);
										setValue("price", calculatedPrice, {
											shouldValidate: true,
											shouldDirty: true,
										});
									}
								}}
								className={`
									px-3 py-1.5 rounded-lg text-xs font-medium
									transition-all duration-200 flex items-center gap-1.5
									${marginPercentage === value ? "bg-mainColor text-white shadow-lg shadow-mainColor/20" : "bg-white text-gray-700 hover:bg-mainColor/10"}
								`}
							>
								<Percent size={12} />
								{label} ({value}%)
							</button>
						))}
					</div>

					<div className="relative">
						<NumericFormat
							value={marginPercentage ?? ""}
							onValueChange={values => {
								const newMargin = values.floatValue;
								if (newMargin !== undefined) {
									setMarginPercentage(newMargin);
									if (costPrice > 0) {
										const calculatedPrice = calculatePriceFromMargin(newMargin);
										setValue("price", calculatedPrice, {
											shouldValidate: true,
											shouldDirty: true,
										});
									}
								}
							}}
							suffix="%"
							decimalScale={2}
							placeholder="Ex: 25,00%"
							thousandSeparator="."
							decimalSeparator=","
							customInput={Input}
							className={`
								w-full px-4 py-2.5 text-sm transition-all duration-200
								border rounded-lg bg-white
								focus:outline-none focus:ring-2 focus:ring-mainColor/50
								${
									marginPercentage !== undefined && marginPercentage > 0
										? marginPercentage < 15
											? "border-red-300 bg-red-50/50"
											: marginPercentage > 50
												? "border-yellow-300 bg-yellow-50/50"
												: "border-green-300 bg-green-50/50"
										: "border-gray-200"
								}
							`}
						/>
						{marginPercentage !== undefined && marginPercentage > 0 && (
							<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="absolute right-3 top-1/2 -translate-y-1/2">
								{marginPercentage < 15 ? (
									<AlertCircle size={16} className="text-red-500" />
								) : marginPercentage > 50 ? (
									<AlertCircle size={16} className="text-yellow-500" />
								) : (
									<CheckCircle2 size={16} className="text-green-500" />
								)}
							</motion.div>
						)}
					</div>

					{marginPercentage !== undefined && marginPercentage > 0 && (
						<motion.div
							initial={{ opacity: 0, y: -10 }}
							animate={{ opacity: 1, y: 0 }}
							className={`text-xs font-medium ${
								marginPercentage < 15 ? "text-red-500" : marginPercentage > 50 ? "text-yellow-500" : "text-green-500"
							}`}
						>
							{marginPercentage < 15
								? "Atenção: Margem muito baixa"
								: marginPercentage > 50
									? "Aviso: Margem muito alta"
									: "Margem dentro do recomendado"}
						</motion.div>
					)}
				</div>

				<div className="relative">
					<InputField
						label="Preço de Venda"
						name="price"
						type="number"
						placeholder="Ex: 432,00"
						icon={<DollarSign size={16} />}
						methods={methods}
						onChange={e => {
							const value = e.target.value;
							if (!value || value.trim() === "") {
								handlePriceChange(0);
								return;
							}

							const numericValue = convertToNumber(value);
							if (numericValue >= 0) {
								handlePriceChange(numericValue);
							}
						}}
					/>
				</div>

				{(costPrice > 0 || price > 0) && (
					<motion.div
						initial={{ opacity: 0, y: -10 }}
						animate={{ opacity: 1, y: 0 }}
						className="bg-gray-50 backdrop-blur-sm rounded-xl p-5 border border-gray-100 space-y-4"
					>
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<div className="p-1.5 bg-mainColor/10 rounded-lg">
									<Calculator size={18} className="text-mainColor" />
								</div>
								<span className="text-sm font-medium text-gray-700">Resumo dos Valores</span>
							</div>
						</div>

						<div className="grid grid-cols-2 gap-4">
							<div className="bg-white rounded-lg p-3 border border-gray-100">
								<span className="text-xs text-gray-500">Lucro</span>
								<p className="text-lg font-semibold text-mainColor">{formatCurrency(calculateProfit().value)}</p>
							</div>

							<div className="bg-white rounded-lg p-3 border border-gray-100">
								<span className="text-xs text-gray-500">Margem de Venda</span>
								<p className={`text-lg font-semibold ${calculateProfit().margin >= 0 ? "text-green-600" : "text-red-600"}`}>
									{calculateProfit().margin.toFixed(2)}%
								</p>
							</div>
						</div>

						<div className="grid grid-cols-1 gap-4">
							<div className="bg-white rounded-lg p-3 border border-gray-100">
								<span className="text-xs text-gray-500">ROI</span>
								<p className="text-lg font-semibold text-blue-600">{calculateProfit().roi.toFixed(2)}%</p>
							</div>
						</div>

						{calculateProfit().margin < 0 && (
							<motion.div
								initial={{ opacity: 0, scale: 0.95 }}
								animate={{ opacity: 1, scale: 1 }}
								className="flex items-center gap-2 bg-red-50 text-red-600 p-3 rounded-lg border border-red-100"
							>
								<AlertCircle size={16} />
								<span className="text-sm font-medium">Atenção: Preço de venda menor que o custo!</span>
							</motion.div>
						)}
					</motion.div>
				)}
			</div>

			<motion.button
				whileHover={{ scale: 1.01 }}
				whileTap={{ scale: 0.99 }}
				type="submit"
				disabled={isSubmitting}
				className={`
					w-full mt-6 py-3 px-4 rounded-xl text-white font-medium
					flex items-center justify-center gap-2
					shadow-lg shadow-mainColor/20 transition-all duration-300
					${isSubmitting ? "bg-gray-400 cursor-not-allowed" : "bg-mainColor hover:bg-mainColor/90 hover:shadow-xl hover:shadow-mainColor/30"}
				`}
				onClick={onSubmit}
			>
				{isSubmitting ? (
					<>
						<div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
						<span>Salvando...</span>
					</>
				) : (
					<>
						<CheckCircle2 size={18} />
						<span>{isInEditMode ? "Salvar Produto" : "Adicionar Produto"}</span>
					</>
				)}
			</motion.button>
		</CardLMPContainer>
	);
};
