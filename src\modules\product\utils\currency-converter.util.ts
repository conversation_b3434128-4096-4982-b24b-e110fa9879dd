/**
 * Utilitário para conversão e formatação de valores monetários
 * Garante consistência na manipulação de preços em todo o sistema
 */

/**
 * Converte um valor para número, tratando strings formatadas como moeda
 * @param value - Valor a ser convertido (number, string, null, undefined)
 * @returns Número válido ou 0 se inválido
 */
export const convertToNumber = (value: number | string | null | undefined): number => {
	// Se é null, undefined ou string vazia, retorna 0
	if (value === null || value === undefined || value === "") {
		return 0;
	}

	// Se já é um número válido, retorna ele
	if (typeof value === "number" && !isNaN(value)) {
		return value;
	}

	// Se é string, limpa formatação e converte
	if (typeof value === "string") {
		// Remove símbolos de moeda, espaços e pontos (separadores de milhares)
		// Substitui vírgula por ponto (separador decimal)
		const cleanValue = value.replace(/[R$\s.]/g, "").replace(",", ".");
		const numericValue = parseFloat(cleanValue);
		return isNaN(numericValue) ? 0 : numericValue;
	}

	return 0;
};

/**
 * Formata um número como moeda brasileira
 * @param value - Valor numérico
 * @returns String formatada como R$ 0,00
 */
export const formatCurrency = (value: number): string => {
	return new Intl.NumberFormat("pt-BR", {
		style: "currency",
		currency: "BRL",
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}).format(value);
};

/**
 * Valida se um valor é um preço válido (maior que 0)
 * @param value - Valor a ser validado
 * @returns true se é um preço válido
 */
export const isValidPrice = (value: number | string | null | undefined): boolean => {
	const numericValue = convertToNumber(value);
	return numericValue > 0;
};

/**
 * Garante que um valor seja um número válido para uso em formulários
 * @param value - Valor de entrada
 * @returns Número válido ou undefined para campos vazios
 */
export const ensureValidFormValue = (value: number | string | null | undefined): number | undefined => {
	if (value === null || value === undefined || value === "") {
		return undefined;
	}
	
	const numericValue = convertToNumber(value);
	return numericValue === 0 ? undefined : numericValue;
};
