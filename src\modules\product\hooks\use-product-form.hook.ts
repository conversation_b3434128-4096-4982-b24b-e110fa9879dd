import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { ProductFormData, productFormSchema } from "../validators/product/product-form.schema";

interface UseProductFormProps {
	defaultValues?: Partial<ProductFormData>;
	onSubmit: (data: ProductFormData) => Promise<void>;
}

export const useProductForm = ({ defaultValues, onSubmit }: UseProductFormProps) => {
	const methods = useForm<ProductFormData>({
		resolver: zodResolver(productFormSchema),
		defaultValues: {
			costPrice: 0,
			price: 0,
			...defaultValues,
		},
		mode: "onChange",
	});

	const handleSubmit = methods.handleSubmit(onSubmit, (errors) => {
		console.error("Validation errors:", errors);
	});

	return {
		methods,
		handleSubmit,
		isSubmitting: methods.formState.isSubmitting,
	};
};
