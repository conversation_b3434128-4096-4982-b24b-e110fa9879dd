import { type ICreateProductDto } from "@/modules/product/dtos/product/create-product.dto";
import { useCreateProduct } from "@/modules/product/hooks/product/create-product.hook";
import { useProductForm } from "@/modules/product/hooks/use-product-form.hook";
import { convertToNumber } from "@/modules/product/utils/currency-converter.util";
import { type ProductFormData } from "@/modules/product/validators/product/product-form.schema";
import { ProductForm } from "../../product-form/product-form";

export const AddProduct = () => {
	const { createProduct } = useCreateProduct();

	const handleSubmit = async (data: Readonly<ProductFormData>): Promise<void> => {
		const productData: ICreateProductDto = {
			name: data.name,
			description: data.description ?? "",
			costPrice: convertToNumber(data.costPrice),
			price: convertToNumber(data.price),
			barcode: data.barcode ?? "",
			ncm: data.ncm ?? "",
			categoryId: Number(data.categoryId),
			image: data.images?.[0] ?? null,
			package:
				data.packages && data.packages.length > 0
					? {
							name: data.packages[0].name,
							barcode: data.packages[0].barcode,
							code: data.packages[0].code,
							quantityPerPackage: data.packages[0].quantityPerPackage,
						}
					: undefined,
			supplierId: Number(data.supplierId),
		};
		createProduct(productData);
	};

	const formMethods = useProductForm({
		onSubmit: handleSubmit,
		defaultValues: {
			packages: [],
		},
	});

	return <ProductForm methods={formMethods.methods} onSubmit={formMethods.handleSubmit} isSubmitting={formMethods.isSubmitting} />;
};
